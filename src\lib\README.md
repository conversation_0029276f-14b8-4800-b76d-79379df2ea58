# API Client Documentation

Dự án này sử dụng một hệ thống API client thống nhất dựa trên Axios để kết nối với backend API.

## Cấu trúc

### 1. `api-client.ts` - Core API Client
- **axiosInstance**: Instance chính của Axios với cấu hình tự động
- **TokenManager**: Quản lý access token và refresh token (client-side)
- **createServerApiCall**: Helper function cho server actions
- **ApiError**: Custom error class

### 2. `client-api.ts` - Client-side API Helper
- **clientApi**: Wrapper functions cho client-side components
- Tự động xử lý lỗi và token refresh

## Cách sử dụng

### Server Actions (Next.js App Router)
```typescript
"use server"

import { createServerApiCall } from '../lib/api-client';
import { cookies } from 'next/headers';

// Tạo function để lấy token từ cookies
const getTokenFromCookies = async (): Promise<string | null> => {
  const cookieStore = await cookies();
  return cookieStore.get('accessToken')?.value || null;
};

// Tạo API call function
const apiCall = createServerApiCall(getTokenFromCookies);

// Sử dụng trong action
export const getUser = async (): Promise<UserProfile> => {
  return await apiCall<UserProfile>('/api/users/profile', {
    method: 'GET',
  });
};
```

### Client Components
```typescript
"use client"

import { clientApi } from '@/lib/client-api';
// hoặc
import { apiClient } from '@/lib/api-client';

// Sử dụng clientApi (recommended)
const fetchUser = async () => {
  try {
    const user = await clientApi.get<UserProfile>('/api/users/profile');
    return user;
  } catch (error) {
    console.error('Failed to fetch user:', error);
  }
};

// Hoặc sử dụng apiClient trực tiếp
const fetchUserDirect = async () => {
  try {
    const response = await apiClient.get('/api/users/profile');
    return response.data;
  } catch (error) {
    console.error('Failed to fetch user:', error);
  }
};
```

## Tính năng

### 1. Tự động thêm Authorization Header
- Client-side: Lấy token từ localStorage
- Server-side: Lấy token từ cookies

### 2. Tự động Refresh Token
- Khi nhận 401 Unauthorized, tự động thử refresh token
- Nếu refresh thành công, retry request gốc
- Nếu refresh thất bại, redirect về login page

### 3. Error Handling
- Thống nhất error format với ApiError class
- Tự động parse error messages từ response

### 4. TypeScript Support
- Full type safety với generic types
- Tự động type inference

## Configuration

### Environment Variables
```env
NEXT_PUBLIC_API_URL=http://localhost:3000
```

### API Config
```typescript
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000',
  TIMEOUT: 10000,
  HEADERS: {
    'Content-Type': 'application/json',
    'User-Agent': 'Trustay-Frontend/1.0',
  },
};
```

## Migration từ fetch()

### Trước (fetch)
```typescript
const response = await fetch(`${API_BASE_URL}/api/users`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
  },
  body: JSON.stringify(data),
});

if (!response.ok) {
  throw new Error('Request failed');
}

const result = await response.json();
```

### Sau (axiosInstance)
```typescript
// Server action
const result = await apiCall('/api/users', {
  method: 'POST',
  data: data,
});

// Client component
const result = await clientApi.post('/api/users', data);
```

## Best Practices

1. **Server Actions**: Luôn sử dụng `createServerApiCall`
2. **Client Components**: Sử dụng `clientApi` cho convenience
3. **Error Handling**: Luôn wrap API calls trong try-catch
4. **TypeScript**: Sử dụng generic types cho type safety
5. **Token Management**: Không manually handle tokens, để interceptors xử lý
