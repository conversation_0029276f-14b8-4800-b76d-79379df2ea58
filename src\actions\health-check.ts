"use server"

// Health check utility to test API connection
import { createServerApiCall } from '../lib/api-client';

// Create API call function for health checks (no auth needed)
const apiCall = createServerApiCall(() => null);

export const healthCheck = async (): Promise<boolean> => {
  try {
    await apiCall('/');
    return true;
  } catch (error) {
    console.error('Health check failed:', error);
    return false;
  }
};

export const apiHealthCheck = async (): Promise<boolean> => {
  try {
    await apiCall('/');
    return true;
  } catch (error) {
    console.error('API health check failed:', error);
    return false;
  }
};
