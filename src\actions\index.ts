// Export all actions for easy importing

// Export API client utilities
export {
  API_CONFIG,
  TokenManager,
  apiClient,
  ApiError,
  createServerApiCall
} from '../lib/api-client';

// Export client-side API utilities
export { clientApi } from '../lib/client-api';

// Export types (prefer types.ts for ApiResponse)
export * from '../types/types';

// Export auth actions (excluding conflicting ApiError and updateUserProfile)
export {
  sendEmailVerification,
  verifyEmailCode,
  registerWithVerification,
  registerDirect,
  login,
  getCurrentUser,
  refreshToken,
  logout,
  completeRegistration,
  skipProfileUpdate
} from './auth-actions';

// Export user actions (prefer user-actions updateUserProfile, exclude ApiError)
export {
  getUserProfile,
  updateUserProfile
} from './user-actions';

// Export location actions (excluding ApiError)
export {
  getProvinces,
  getDistrictsByProvince,
  getWardsByDistrict
} from './location-actions';
export * from './health-check';
